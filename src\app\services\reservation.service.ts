import { Injectable } from '@angular/core';
import { Observable, of, throwError } from 'rxjs';
import { catchError, map, switchMap, tap } from 'rxjs/operators';
import { Reservation, ReservationFormData } from '../models/reservation.model';
import { Table } from '../models/table.model';
import { ApiService } from './api.service';
import { AuthService } from './auth.service';

interface StrapiResponse<T> {
  data: Array<{
    id: number;
    attributes: T & {
      createdAt: string;
      updatedAt: string;
      publishedAt: string;
    };
  }>;
  meta: {
    pagination: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

interface StrapiSingleResponse<T> {
  data: {
    id: number;
    attributes: T & {
      createdAt: string;
      updatedAt: string;
      publishedAt: string;
    };
  };
  meta: {};
}

interface StrapiTableAttributes {
  tableNumber: number;
  capacity: number;
  isAvailable: boolean;
  positionX: number;
  positionY: number;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  event: {
    data: {
      id: number;
    };
  };
}

interface StrapiReservationAttributes {
  id: number;
  documentId: string;
  ticket_count: number;
  statutR: 'pending' | 'confirmed' | 'cancelled' | null;
  total_price: number;
  booking_date: string | null;
  payment_reference: string | null;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  user: number; // Direct user ID
  event: number; // Direct event ID
}

@Injectable({
  providedIn: 'root'
})
export class ReservationService {
  // Fallback tables for development if API is not available
  private fallbackTables: Table[] = [
    {
      id: '1',
      eventId: '1',
      tableNumber: 1,
      capacity: 4,
      isAvailable: true,
      positionX: 20,
      positionY: 30,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: '2',
      eventId: '1',
      tableNumber: 2,
      capacity: 6,
      isAvailable: true,
      positionX: 50,
      positionY: 30,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: '3',
      eventId: '1',
      tableNumber: 3,
      capacity: 8,
      isAvailable: true,
      positionX: 80,
      positionY: 30,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  ];

  constructor(
    private apiService: ApiService,
    private authService: AuthService
  ) { }

  /**
   * Map Strapi table to our Table model
   */
  private mapStrapiTable(tableData: { id: number; attributes: StrapiTableAttributes }): Table {
    return {
      id: tableData.id.toString(),
      eventId: tableData.attributes.event.data.id.toString(),
      tableNumber: tableData.attributes.tableNumber,
      capacity: tableData.attributes.capacity,
      isAvailable: tableData.attributes.isAvailable,
      positionX: tableData.attributes.positionX,
      positionY: tableData.attributes.positionY,
      createdAt: tableData.attributes.createdAt,
      updatedAt: tableData.attributes.updatedAt
    };
  }

  /**
   * Map Strapi reservation to our Reservation model
   */
  private mapStrapiReservation(reservationData: any): Reservation {
    // New Strapi format - fields are directly on the object
    console.log('Mapping reservation data:', reservationData);

    return {
      id: reservationData.id?.toString() || reservationData.documentId?.toString() || '0',
      userId: reservationData.user?.id?.toString() || reservationData.user?.toString() || '0', // Handle both populated and non-populated user
      eventId: reservationData.event?.id?.toString() || reservationData.event?.toString() || '0', // Handle both populated and non-populated event
      tableId: '0', // Default value since table is not in the new schema
      numberOfGuests: reservationData.ticket_count || 0,
      status: reservationData.statutR || 'pending', // Use statutR field
      totalPrice: reservationData.total_price || 0,
      paymentIntentId: reservationData.payment_reference || '',
      specialRequests: '', // Not in the new schema
      createdAt: reservationData.booking_date || reservationData.createdAt || new Date().toISOString(),
      updatedAt: reservationData.updatedAt || new Date().toISOString()
    };
  }

  /**
   * Get tables by event ID
   */
  getTablesByEventId(eventId: string): Observable<Table[]> {
    return this.apiService.get<StrapiResponse<StrapiTableAttributes>>(
      'tables?populate=*'
    ).pipe(
      switchMap(response => {
        // Client-side filtering for tables by event ID
        const tables = response.data
          .filter(tableData => tableData.attributes.event.data.id.toString() === eventId)
          .map(tableData => this.mapStrapiTable(tableData));

        // If no tables found, generate tables based on event capacity
        if (tables.length === 0) {
          return this.generateTablesForEvent(eventId);
        }
        return of(tables);
      }),
      catchError(error => {
        console.error(`Error fetching tables for event ${eventId}`, error);
        // Generate tables on error
        return this.generateTablesForEvent(eventId);
      })
    );
  }

  /**
   * Generate tables for an event based on max_attendees
   */
  private generateTablesForEvent(eventId: string): Observable<Table[]> {
    // First get the event to access max_attendees
    return this.apiService.get<any>('events?populate=*').pipe(
      map(eventResponse => {
        let maxAttendees = 0;

        // Handle both old and new Strapi formats
        if (eventResponse.data && Array.isArray(eventResponse.data)) {
          // Client-side filtering to find the event by ID
          const eventData = eventResponse.data.find((event: any) =>
            event.id.toString() === eventId || event.documentId === eventId
          );

          if (eventData) {
            if ('max_attendees' in eventData) {
              // New format
              maxAttendees = eventData.max_attendees || 0;
            } else if (eventData.attributes && 'maxCapacity' in eventData.attributes) {
              // Old format
              maxAttendees = eventData.attributes.maxCapacity || 0;
            }
          }
        }

        // Default to 50 if no max_attendees found
        if (!maxAttendees || maxAttendees <= 0) {
          maxAttendees = 50;
        }

        return this.createTablesBasedOnCapacity(eventId, maxAttendees);
      }),
      catchError(error => {
        console.error(`Error fetching event ${eventId} for table generation`, error);
        // Default to 50 attendees if event fetch fails
        return of(this.createTablesBasedOnCapacity(eventId, 50));
      })
    );
  }

  /**
   * Create tables based on total capacity needed
   */
  private createTablesBasedOnCapacity(eventId: string, totalCapacity: number): Table[] {
    const tables: Table[] = [];
    let remainingCapacity = totalCapacity;
    let tableCount = 0;

    // Calculate how many tables we need
    // Strategy: Create a mix of different sized tables
    while (remainingCapacity > 0 && tableCount < 20) { // Cap at 20 tables max
      tableCount++;

      // Determine table capacity based on remaining needs
      let tableCapacity = 4; // Default size

      if (remainingCapacity > 50) {
        // For large events, add some big tables
        tableCapacity = tableCount % 3 === 0 ? 8 : (tableCount % 2 === 0 ? 6 : 4);
      } else if (remainingCapacity > 20) {
        // For medium events, mix of medium and small
        tableCapacity = tableCount % 2 === 0 ? 6 : 4;
      } else {
        // For small remaining capacity, smaller tables
        tableCapacity = Math.min(4, remainingCapacity);
      }

      // Calculate position (grid layout with some randomness for natural look)
      // Create a more restaurant-like layout
      const row = Math.floor((tableCount-1) / 4);
      const col = (tableCount-1) % 4;

      // Add slight randomness to positions for more natural look
      const randomOffsetX = Math.floor(Math.random() * 10) - 5;
      const randomOffsetY = Math.floor(Math.random() * 10) - 5;

      const posX = 15 + (col * 25) + randomOffsetX;
      const posY = 15 + (row * 25) + randomOffsetY;

      tables.push({
        id: `temp_${eventId}_${tableCount}`,
        eventId: eventId,
        tableNumber: tableCount,
        capacity: tableCapacity,
        isAvailable: true,
        positionX: posX,
        positionY: posY,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });

      remainingCapacity -= tableCapacity;
    }

    return tables;
  }

  /**
   * Get table by ID
   */
  getTableById(tableId: string): Observable<Table | undefined> {
    return this.apiService.get<any>('tables?populate=*').pipe(
      map(response => {
        if (response && response.data && Array.isArray(response.data)) {
          // Client-side filtering to find table by ID
          const tableData = response.data.find((table: any) =>
            table.id.toString() === tableId || table.documentId === tableId
          );

          if (tableData) {
            return this.mapStrapiTable(tableData);
          }
        }

        return undefined;
      }),
      catchError(error => {
        console.error(`Error fetching table with ID ${tableId}`, error);
        // Fallback to mock data for development
        const fallbackTable = this.fallbackTables.find(t => t.id === tableId);
        return fallbackTable ? of(fallbackTable) : throwError(() => new Error('Table not found'));
      })
    );
  }

  /**
   * Create a new reservation
   */
  createReservation(eventId: string, tableId: string, formData: ReservationFormData, totalPrice: number): Observable<Reservation> {
    const currentUser = this.authService.getCurrentUser();
    if (!currentUser) {
      return throwError(() => new Error('User not authenticated'));
    }

    // Map the form data to match Strapi schema field names
    // Try different user field formats to see what works
    const reservationData = {
      ticket_count: formData.numberOfGuests,
      total_price: totalPrice,
      booking_date: new Date().toISOString(),
      statutR: 'pending',
      user: currentUser.id, // Try string user ID first
      event: parseInt(eventId) // Convert to number for Strapi relation
    };

    console.log('Creating reservation with data:', reservationData);
    console.log('Current user:', currentUser);
    console.log('Event ID:', eventId);

    return this.apiService.post<any>(
      'reservations',
      { data: reservationData }
    ).pipe(
      map(response => {
        console.log('Reservation created successfully:', response);
        // Skip table availability update since tables don't exist in Strapi
        // this.updateTableAvailability(tableId, false).subscribe();

        // Handle the response format - Strapi returns { data: reservationObject }
        const reservationData = response.data;
        return this.mapStrapiReservation(reservationData);
      }),
      catchError(error => {
        console.error('Error creating reservation', error);
        console.error('Reservation data sent:', reservationData);
        console.error('Full error response:', error.error);
        return throwError(() => new Error(error.message || 'Erreur lors de la création de la réservation'));
      })
    );
  }

  /**
   * Update table availability
   */
  private updateTableAvailability(tableId: string, isAvailable: boolean): Observable<any> {
    // First get the table to get its ID
    return this.getTableById(tableId).pipe(
      switchMap(table => {
        if (!table) {
          return throwError(() => new Error(`Table with documentId ${tableId} not found`));
        }

        return this.apiService.put(
          `tables/${table.id}`,
          { data: { isAvailable } }
        );
      }),
      catchError(error => {
        console.error(`Error updating table availability for table ${tableId}`, error);
        return throwError(() => new Error(error.message || 'Erreur lors de la mise à jour de la disponibilité de la table'));
      })
    );
  }

  /**
   * Get reservation by ID
   */
  getReservationById(reservationId: string): Observable<Reservation | undefined> {
    return this.apiService.get<any>('reservations?populate=*').pipe(
      map(response => {
        if (response && response.data && Array.isArray(response.data)) {
          // Client-side filtering to find reservation by ID
          const reservationData = response.data.find((reservation: any) =>
            reservation.id.toString() === reservationId || reservation.documentId === reservationId
          );

          if (reservationData) {
            return this.mapStrapiReservation(reservationData);
          }
        }

        return undefined;
      }),
      catchError(error => {
        console.error(`Error fetching reservation with ID ${reservationId}`, error);
        return throwError(() => new Error(error.message || 'Erreur lors de la récupération de la réservation'));
      })
    );
  }

  /**
   * Get user reservations
   */
  getUserReservations(userId: string): Observable<Reservation[]> {
    return this.apiService.get<any>('reservations?populate=*').pipe(
      map(response => {
        if (response && response.data && Array.isArray(response.data)) {
          // Client-side filtering for user reservations
          const userReservations = response.data
            .filter((reservation: any) =>
              reservation.attributes?.user?.data?.id?.toString() === userId ||
              reservation.user?.toString() === userId
            )
            .map((reservationData: any) => this.mapStrapiReservation(reservationData))
            .sort((a: Reservation, b: Reservation) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

          return userReservations;
        }

        return [];
      }),
      catchError(error => {
        console.error(`Error fetching reservations for user ${userId}`, error);
        return throwError(() => new Error(error.message || 'Erreur lors de la récupération des réservations'));
      })
    );
  }

  /**
   * Update reservation status
   */
  updateReservationStatus(reservationId: string, status: 'pending' | 'confirmed' | 'cancelled', paymentReference?: string): Observable<Reservation | undefined> {
    const updateData: any = { statutR: status }; // Use statutR field name
    if (paymentReference) {
      updateData.payment_reference = paymentReference;
    }

    // First get the reservation to get its ID
    return this.getReservationById(reservationId).pipe(
      switchMap(reservation => {
        if (!reservation) {
          return throwError(() => new Error(`Reservation with documentId ${reservationId} not found`));
        }

        return this.apiService.put<StrapiSingleResponse<StrapiReservationAttributes>>(
          `reservations/${reservation.id}`,
          { data: updateData }
        );
      }),
      map(response => {
        // Handle single response format
        return this.mapStrapiReservation(response.data);
      }),
      catchError(error => {
        console.error(`Error updating reservation status for reservation ${reservationId}`, error);
        return throwError(() => new Error(error.message || 'Erreur lors de la mise à jour du statut de la réservation'));
      })
    );
  }

  /**
   * Cancel reservation
   */
  cancelReservation(reservationId: string): Observable<boolean> {
    return this.getReservationById(reservationId).pipe(
      tap(reservation => {
        if (!reservation) {
          throw new Error('Reservation not found');
        }

        // Update table availability
        this.updateTableAvailability(reservation.tableId, true).subscribe();
      }),
      switchMap(() => {
        return this.updateReservationStatus(reservationId, 'cancelled');
      }),
      map(() => true),
      catchError(error => {
        console.error(`Error cancelling reservation ${reservationId}`, error);
        return throwError(() => new Error(error.message || 'Erreur lors de l\'annulation de la réservation'));
      })
    );
  }
}
